[2025-07-29 08:30:20] local.ERROR: Unparenthesized `a ? b : c ?: d` is not supported. Use either `(a ? b : c) ?: d` or `a ? b : (c ?: d)` {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Unparenthesized `a ? b : c ?: d` is not supported. Use either `(a ? b : c) ?: d` or `a ? b : (c ?: d)` at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\ServiceController.php:110)
[stacktrace]
#0 {main}
"} 
[2025-07-29 08:30:21] local.ERROR: Unparenthesized `a ? b : c ?: d` is not supported. Use either `(a ? b : c) ?: d` or `a ? b : (c ?: d)` {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Unparenthesized `a ? b : c ?: d` is not supported. Use either `(a ? b : c) ?: d` or `a ? b : (c ?: d)` at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\ServiceController.php:110)
[stacktrace]
#0 {main}
"} 
[2025-07-29 08:31:35] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:35] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:31:35] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:35] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:31:35] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:31:35] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250703-961"} 
[2025-07-29 08:31:35] testing.INFO: Complaint status update job dispatched {"ticket_number":"LDH-20250703-961","previous_status":"pending","new_status":"in_progress"} 
[2025-07-29 08:31:35] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:31:37] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(166): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:161}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(98): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_can_view_individual_potential_details()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#80 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#81 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#82 {main}
"} 
[2025-07-29 08:31:37] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(178): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:175}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(107): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_shows_image_gallery_when_available()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#80 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#81 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#82 {main}
"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250504-222","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250504-222"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250510-902","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250510-902"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250503-087","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250503-087"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250505-067","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250505-067"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250525-033","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250525-033"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250604-802","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250604-802"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250522-281","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250522-281"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250526-712","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250526-712"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250719-641","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250719-641"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250616-020","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250616-020"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250617-294","admin_email":"<EMAIL>"} 
[2025-07-29 08:31:38] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250617-294"} 
[2025-07-29 08:37:17] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(152): Illuminate\\Http\\Request->session()
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(71): App\\Exceptions\\Handler->handleHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php(39): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(718): {closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php:33}:35}(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(619): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#38 {main}
"} 
[2025-07-29 08:37:17] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(152): Illuminate\\Http\\Request->session()
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(71): App\\Exceptions\\Handler->handleHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php(39): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(718): {closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php:33}:35}(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(619): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#38 {main}
"} 
[2025-07-29 08:37:17] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(152): Illuminate\\Http\\Request->session()
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(71): App\\Exceptions\\Handler->handleHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php(39): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(718): {closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php:33}:35}(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(619): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#38 {main}
"} 
[2025-07-29 08:37:17] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(152): Illuminate\\Http\\Request->session()
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(71): App\\Exceptions\\Handler->handleHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php(39): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(718): {closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php:33}:35}(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(619): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#38 {main}
"} 
[2025-07-29 08:37:17] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(152): Illuminate\\Http\\Request->session()
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(71): App\\Exceptions\\Handler->handleHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php(39): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(718): {closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php:33}:35}(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(619): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#38 {main}
"} 
[2025-07-29 08:37:17] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(152): Illuminate\\Http\\Request->session()
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(71): App\\Exceptions\\Handler->handleHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php(39): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(718): {closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php:33}:35}(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(619): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#38 {main}
"} 
[2025-07-29 08:37:19] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(152): Illuminate\\Http\\Request->session()
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(71): App\\Exceptions\\Handler->handleHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php(39): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(718): {closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php:33}:35}(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(619): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#38 {main}
"} 
[2025-07-29 08:37:19] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(152): Illuminate\\Http\\Request->session()
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(71): App\\Exceptions\\Handler->handleHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php(39): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(718): {closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php:33}:35}(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(619): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#38 {main}
"} 
[2025-07-29 08:37:19] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(152): Illuminate\\Http\\Request->session()
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(71): App\\Exceptions\\Handler->handleHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php(39): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(718): {closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php:33}:35}(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(619): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#38 {main}
"} 
[2025-07-29 08:37:27] local.ERROR: Session store not set on request. {"exception":"[object] (RuntimeException(code: 0): Session store not set on request. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php:584)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(152): Illuminate\\Http\\Request->session()
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Exceptions\\Handler.php(71): App\\Exceptions\\Handler->handleHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php(39): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(718): {closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\bootstrap\\app.php:33}:35}(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException), Object(Illuminate\\Http\\Request))
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(619): Illuminate\\Foundation\\Exceptions\\Handler->renderViaCallbacks(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#37 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#38 {main}
"} 
[2025-07-29 08:37:55] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(166): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:161}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(98): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_can_view_individual_potential_details()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#80 {main}
"} 
[2025-07-29 08:37:55] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(178): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:175}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(107): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_shows_image_gallery_when_available()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#80 {main}
"} 
[2025-07-29 08:40:13] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:13] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:40:14] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:14] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:40:14] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:40:14] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250429-048"} 
[2025-07-29 08:40:14] testing.INFO: Complaint status update job dispatched {"ticket_number":"LDH-20250429-048","previous_status":"pending","new_status":"in_progress"} 
[2025-07-29 08:40:14] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:40:15] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(166): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:161}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(98): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_can_view_individual_potential_details()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#80 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#81 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#82 {main}
"} 
[2025-07-29 08:40:15] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(178): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:175}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(107): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_shows_image_gallery_when_available()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#80 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#81 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#82 {main}
"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250710-040","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250710-040"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250626-693","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250626-693"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250713-540","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250713-540"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250527-273","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250527-273"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250727-443","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250727-443"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250602-593","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250602-593"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250616-622","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250616-622"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250620-940","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250620-940"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250520-693","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250520-693"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250509-755","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250509-755"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250516-552","admin_email":"<EMAIL>"} 
[2025-07-29 08:40:16] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250516-552"} 
[2025-07-29 08:43:55] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:55] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:43:55] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:55] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:43:56] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:43:56] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250523-950"} 
[2025-07-29 08:43:56] testing.INFO: Complaint status update job dispatched {"ticket_number":"LDH-20250523-950","previous_status":"pending","new_status":"in_progress"} 
[2025-07-29 08:43:56] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:43:58] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(166): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:161}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(98): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_can_view_individual_potential_details()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#80 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#81 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#82 {main}
"} 
[2025-07-29 08:43:58] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(178): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:175}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(107): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_shows_image_gallery_when_available()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#80 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#81 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#82 {main}
"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250624-884","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250624-884"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250721-599","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250721-599"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250515-875","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250515-875"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250707-309","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250707-309"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250716-377","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250716-377"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250502-724","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250502-724"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250728-367","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250728-367"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250519-817","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250519-817"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250509-146","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250509-146"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250609-165","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250609-165"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250629-210","admin_email":"<EMAIL>"} 
[2025-07-29 08:43:59] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250629-210"} 
[2025-07-29 08:45:42] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:45:42] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:45:42] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:45:42] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:48:35] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:48:35] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:48:35] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:48:35] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:48:35] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:51:58] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:51:58] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:51:58] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:51:58] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:51:58] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:52:33] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:52:33] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:52:33] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:52:33] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:53:39] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:53:39] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:53:39] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:53:39] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:53:39] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:54:19] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:19] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:54:19] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:19] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:54:19] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:54:19] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250701-302"} 
[2025-07-29 08:54:19] testing.INFO: Complaint status update job dispatched {"ticket_number":"LDH-20250701-302","previous_status":"pending","new_status":"in_progress"} 
[2025-07-29 08:54:19] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:54:21] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(166): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:161}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(98): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_can_view_individual_potential_details()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#80 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#81 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#82 {main}
"} 
[2025-07-29 08:54:21] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(178): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:175}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(107): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_shows_image_gallery_when_available()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#80 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#81 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#82 {main}
"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250609-827","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250609-827"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250602-177","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250602-177"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250619-156","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250619-156"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250515-637","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250515-637"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250707-927","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250707-927"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250516-913","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250516-913"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250507-825","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250507-825"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250629-012","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250629-012"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250520-689","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250520-689"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250629-113","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250629-113"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250620-229","admin_email":"<EMAIL>"} 
[2025-07-29 08:54:21] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250620-229"} 
[2025-07-29 08:55:22] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:22] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:55:22] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250729-001","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:22] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250729-001"} 
[2025-07-29 08:55:22] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:55:22] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250614-950"} 
[2025-07-29 08:55:22] testing.INFO: Complaint status update job dispatched {"ticket_number":"LDH-20250614-950","previous_status":"pending","new_status":"in_progress"} 
[2025-07-29 08:55:22] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20240724-001"} 
[2025-07-29 08:55:24] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(166): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:161}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(98): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_can_view_individual_potential_details()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#80 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#81 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#82 {main}
"} 
[2025-07-29 08:55:24] testing.ERROR: Array to string conversion {"exception":"[object] (ErrorException(code: 0): Array to string conversion at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php:173)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():255}(2, 'Array to string...', 'E:\\\\Coding\\\\PHP\\\\L...', 173)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Services\\SEOService.php(67): App\\Services\\SEOService->getPotentialShowMeta(Array, Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Controllers\\PotentialController.php(240): App\\Services\\SEOService->generateMetaTags('potential.show', Array)
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\PotentialController->show(Object(App\\Models\\Potential))
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\PotentialController), 'show')
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/potensi/5', Array, Array, Array, Array)
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php(178): Illuminate\\Foundation\\Testing\\TestCase->get('/potensi/5')
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\PotentialPageTest->{closure:{closure:{closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\tests\\Feature\\PotentialPageTest.php:5}:160}:175}()
#64 [internal function]: P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Factories\\TestCaseMethodFactory::getClosure():158}()
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\PotentialPageTest->{closure:Pest\\Concerns\\Testable::__callClosure():419}()
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\PotentialPageTest->__callClosure(Object(Closure), Array)
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(107): P\\Tests\\Feature\\PotentialPageTest->__runTest(Object(Closure))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\PotentialPageTest->__pest_evaluable__Potential_Page_Feature__→__Potential_Detail__→_it_shows_image_gallery_when_available()
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#72 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#73 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\PotentialPageTest))
#74 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#75 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#76 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#77 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#78 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#79 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#80 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#81 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest(192): {closure:E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\pestphp\\pest\\bin\\pest:18}()
#82 {main}
"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250514-932","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250514-932"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250507-880","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250507-880"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250501-598","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250501-598"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250531-987","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250531-987"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250626-666","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250626-666"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250513-696","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250513-696"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250525-221","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250525-221"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250503-144","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250503-144"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250723-346","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250723-346"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250722-065","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250722-065"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification sent to admin {"ticket_number":"LDH-20250429-352","admin_email":"<EMAIL>"} 
[2025-07-29 08:55:24] testing.INFO: Complaint notification job dispatched {"ticket_number":"LDH-20250429-352"} 
